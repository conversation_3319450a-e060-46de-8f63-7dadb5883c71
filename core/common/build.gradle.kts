plugins {
    id("avito.android.library")
    id("avito.android.library.compose")
    id("avito.android.hilt")
}

android {
    namespace = "se.scmv.morocco.common"

    flavorDimensions += "environment"
    productFlavors {
        create("prod") {
            dimension = "environment"
        }
        create("pre") {
            dimension = "environment"
        }
    }
}

dependencies {
    implementation(libs.androidx.appcompat)
    implementation(libs.androidx.appcompat.resources)
}