package se.scmv.morocco.data.network.rest.config.dtos

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class ListingFilterCategoriesDto(
    @SerializedName("category") val category: Category,
    @SerializedName("children") val children: List<ListingFilterCategoriesDto>? = emptyList(),
    @SerializedName("icon") val icon: String,
    @SerializedName("index") val index: Int,
    @SerializedName("name") val name: String?,
    @SerializedName("trackingName") val trackingName: String?,
    @SerializedName("urlSlug") val urlSlug: String?
) {
    @Keep
    data class Category(
        @SerializedName("adType") val adType: String,
        @SerializedName("id") val id: String
    )
}

@Keep
data class ListingCategoryFiltersDto(
    @SerializedName("category") var category: Category,
    @SerializedName("steps") var filters: Filters
) {
    @Keep
    data class Category(
        @SerializedName("id") var id: String? = null,
        @SerializedName("adType") var adType: String? = null
    )

    @Keep
    data class Filters(
        @SerializedName("navBarFilters") var navBarFilters: List<BaseFilters> = emptyList(),
        @SerializedName("primaryFilters") var primaryFilters: List<BaseFilters> = emptyList(),
        @SerializedName("secondaryFilters") var secondaryFilters: List<BaseFilters> = emptyList()
    )

    @Keep
    data class BaseFilters(
        @SerializedName("icon") var icon: String? = null,
        @SerializedName("id") var id: String,
        @SerializedName("isParam") var isParam: Boolean? = null,
        @SerializedName("list") var filterItems: List<FilterItems>? = emptyList(),
        @SerializedName("range") val range: List<Int>?,
        @SerializedName("step") val step: Int?,
        @SerializedName("suffix") val suffix: String?,
        @SerializedName("name") var name: String? = null,
        @SerializedName("type") var type: String? = null,
        @SerializedName("child") val child: String? = null,
        @SerializedName("fields") val fields: List<field>? = null,
        @SerializedName("childParam") val childParam: ChildParam? = null,
        @SerializedName("defaultValue") val defaultValue: Boolean = false,
        @SerializedName("dependencies") val dependencies: List<Dependency>? = null,
        @SerializedName("description") val description: String? = ""
    )

    @Keep
    data class Dependency(
        @SerializedName("dependsOn") val dependsOn: String,
        @SerializedName("condition") val condition: Condition
    )

    @Keep
    data class Condition(
        @SerializedName("type") val type: ConditionType
    )

    // TODO Currently the backend only sends notEmpty, but we create this enum class to scale faster later.
    @Keep
    enum class ConditionType {
        @SerializedName("notEmpty")
        NOT_EMPTY,

        @SerializedName("equals")
        EQUALS
    }

    @Keep
    data class field(
        @SerializedName("id") val id: String,
        @SerializedName("isParam") val isParam: String,
        @SerializedName("name") val name: String,
    )

    @Keep
    data class ChildParam(
        val id: String,
        val name: String
    )

    @Keep
    data class FilterItems(
        @SerializedName("key") var key: String? = null,
        @SerializedName("name") var name: String? = null,
        @SerializedName("label") var label: String? = null,
        @SerializedName("short") var short: String? = null,
        @SerializedName("trackingName") var trackingName: String? = null,
        @SerializedName("children") val children: List<Child>? = null

    )

    @Keep
    data class Child(
        @SerializedName("key") val key: String?,
        @SerializedName("label") val label: String?
    )
}