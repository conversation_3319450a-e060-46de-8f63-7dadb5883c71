package se.scmv.morocco.data.database.entities

import androidx.room.ColumnInfo
import androidx.room.Entity

@Entity(tableName = "towns", primaryKeys = ["id", "city_id"])
data class TownEntity(
    @ColumnInfo(name = "id") val id: String,
    @ColumnInfo(name = "name_ar") val nameAr: String,
    @ColumnInfo(name = "name_Fr") val nameFr: String,
    @ColumnInfo(name = "tracking_name") val trackingName: String,
    @ColumnInfo(name = "city_id") val cityId: String
)