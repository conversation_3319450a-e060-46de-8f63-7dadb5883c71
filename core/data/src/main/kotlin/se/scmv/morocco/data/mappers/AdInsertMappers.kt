package se.scmv.morocco.data.mappers

import se.scmv.morocco.data.network.rest.config.dtos.AdInsertFieldDto
import se.scmv.morocco.data.network.rest.config.dtos.AdInsertFieldGroupingDto
import se.scmv.morocco.data.network.rest.config.dtos.AdInsertStepDto
import se.scmv.morocco.data.repository.utils.Constants
import se.scmv.morocco.data.repository.utils.buildOrionComponentIconUrl
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AdInsertMediaType
import se.scmv.morocco.domain.models.AdInsertStep
import se.scmv.morocco.domain.models.orion.OrionAlert
import se.scmv.morocco.domain.models.orion.OrionBaseComponent
import se.scmv.morocco.domain.models.orion.OrionBaseComponentData
import se.scmv.morocco.domain.models.orion.OrionComponentValidation
import se.scmv.morocco.domain.models.orion.OrionGroupHeader
import se.scmv.morocco.domain.models.orion.OrionImageUploader
import se.scmv.morocco.domain.models.orion.OrionKeyStringItem
import se.scmv.morocco.domain.models.orion.OrionKeyStringValue
import se.scmv.morocco.domain.models.orion.OrionLap
import se.scmv.morocco.domain.models.orion.OrionNativeDropdown
import se.scmv.morocco.domain.models.orion.OrionNumberCounter
import se.scmv.morocco.domain.models.orion.OrionSelectExtended
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdown
import se.scmv.morocco.domain.models.orion.OrionSmartDropDown
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownChildData
import se.scmv.morocco.domain.models.orion.OrionSmartDropDownItem
import se.scmv.morocco.domain.models.orion.OrionTextField
import se.scmv.morocco.domain.models.orion.OrionTimePicker
import se.scmv.morocco.domain.models.orion.OrionToggle
import se.scmv.morocco.domain.models.orion.OrionVas
import se.scmv.morocco.domain.models.orion.OrionVideoUploader
import se.scmv.morocco.type.AdMediaType

enum class OrionComponentGroupId(val id: String) {
    VIDEOS_GROUP("videosgroup"),
    LAP_GROUP("lapgroup"),
}

enum class OrionComponentId(val id: String) {
    VAS("vas")
}

internal fun AdInsertStepDto.toAdInsertStep(account: Account.Connected) = AdInsertStep(
    title = title.orEmpty(),
    subtitle = subtitle.orEmpty(),
    tipsTitle = tipsTitle.orEmpty(),
    tipsContent = tips.orEmpty(),
    infoUrl = infoLink?.url.orEmpty(),
    components = fieldGrouping.map { it.toAdInsertStepComponentsGroup(account = account) }.flatten()
)

internal fun AdInsertFieldGroupingDto.toAdInsertStepComponentsGroup(
    account: Account.Connected
): List<OrionBaseComponent> {
    val components = mutableListOf<OrionBaseComponent>(
        OrionGroupHeader(baseData = toOrionBaseComponentData(), subtitle = subtitle.orEmpty())
    )

    val containsOnlyDelivery = fields.size == 1 && fields[0].id == Constants.KEY_DELIVERY
    if (containsOnlyDelivery) {
        when (account) {
            is Account.Connected.Private -> return emptyList()
            is Account.Connected.Shop -> {
                if (account.store.allowedAccess.deliveryAllowed.not()) return emptyList()
            }
        }
    }

    when (id) {
        OrionComponentGroupId.VIDEOS_GROUP.id -> {
            if (
                account is Account.Connected.Private ||
                (account is Account.Connected.Shop && account.store.allowedAccess.adMaxVideos <= 0)
            ) {
                return emptyList()
            }
        }

        OrionComponentGroupId.LAP_GROUP.id -> {
            fields.firstOrNull()?.toOrionBaseComponentData()?.let { lapComponent ->
                components.add(OrionLap(baseData = lapComponent))
            } ?: return emptyList()
        }

        else -> Unit
    }
    components.addAll(fields.mapNotNull { it.toOrionBaseComponent(account = account) })
    val booleans = toOrionBooleans()
    if (booleans != null) {
        components.add(booleans)
    }

    return components
}

internal fun AdInsertFieldDto.toOrionBaseComponent(account: Account.Connected): OrionBaseComponent? {
    return when (type) {
        OrionComponentType.TOGGLE_FIELD.key -> toOrionToggle()
        OrionComponentType.NUMBER_COUNTER.key -> toOrionNumberCounter()
        OrionComponentType.TEXT_FIELD.key -> toOrionTextField(
            inputType = OrionTextField.InputType.TEXT
        )

        OrionComponentType.LARGE_TEXT_FIELD.key -> toOrionTextField(
            isLarge = true,
            inputType = OrionTextField.InputType.TEXT
        )

        OrionComponentType.MEASURE_TEXT_FIELD.key -> toOrionTextField(
            inputType = OrionTextField.InputType.NUMBER
        )

        OrionComponentType.SINGLE_SELECT_EXTENDED.key -> if (!values.isNullOrEmpty()) {
            toOrionSingleSelectExtended()
        } else null

        OrionComponentType.TIME_PICKER.key -> if (!values.isNullOrEmpty()) {
            toOrionTimePicker()
        } else null

        OrionComponentType.NATIVE_DROPDOWN.key -> if (id == Constants.KEY_PHONE) {
            toAccountPhones(account = account)
        } else {
            toOrionNativeDropdown()
        }

        OrionComponentType.MESSAGE_WARNING.key -> toOrionAlter(OrionAlert.Type.WARNING)

        OrionComponentType.MESSAGE_INFO.key -> toOrionAlter(OrionAlert.Type.INFO)

        OrionComponentType.IMAGE_UPLOADER.key -> toOrionImageUploader(account = account)

        OrionComponentType.VIDEO_UPLOADER.key -> toOrionVideoUploader(account = account)

        OrionComponentType.SINGLE_SELECT_CATEGORY_DROPDOWN.key -> toOrionSingleSelectCategoryDropdown()

        OrionComponentType.SINGLE_SELECT_SMART_DROPDOWN.key,
        OrionComponentType.SINGLE_SELECT_SMART_DROPDOWN_ICON.key,
        OrionComponentType.SINGLE_SELECT_SIMPLE_DROPDOWN.key -> toOrionSmartDropdown(account)

        else -> when (id) {
            OrionComponentId.VAS.id -> toOrionVas()
            else -> null
        }
    }
}

internal fun AdInsertFieldDto.toOrionBaseComponentData() = OrionBaseComponentData(
    id = id,
    title = name.orEmpty(),
    required = required ?: false,
    iconUrl = icon?.let { buildOrionComponentIconUrl(it) }.orEmpty(),
    validations = validation?.mapNotNull {
        if (it.regex != null && it.errorMessage != null) {
            OrionComponentValidation(it.errorMessage, it.regex)
        } else null
    }.orEmpty()
)

internal fun AdInsertFieldGroupingDto.toOrionBaseComponentData() = OrionBaseComponentData(
    id = id.orEmpty(),
    title = title.orEmpty(),
    required = false,
    iconUrl = "",
    validations = emptyList()
)

internal fun AdInsertFieldDto.toOrionToggle() = OrionToggle(baseData = toOrionBaseComponentData())

internal fun AdInsertFieldDto.toOrionNumberCounter(): OrionNumberCounter {
    val rangeStart = range?.firstOrNull()?.toIntOrNull() ?: 0
    val rangeEnd = range?.lastOrNull()?.toIntOrNull() ?: 0
    return OrionNumberCounter(
        baseData = toOrionBaseComponentData(),
        range = rangeStart to rangeEnd
    )
}

internal fun AdInsertFieldDto.toOrionTextField(
    inputType: OrionTextField.InputType,
    isLarge: Boolean = false,
    enabled: Boolean = true,
    potentialValue: String? = null
) = OrionTextField(
    baseData = toOrionBaseComponentData(),
    enabled = enabled,
    isLarge = isLarge,
    suffix = suffix,
    inputType = inputType,
    potentialValue = potentialValue,
    notifyLapTitle = id == Constants.KEY_TITLE,
    notifyLapDescription = id == Constants.KEY_DESCRIPTION,
)

internal fun AdInsertFieldDto.toOrionSingleSelectExtended() = OrionSelectExtended(
    baseData = toOrionBaseComponentData(),
    allowMultiSelect = false,
    items = values?.mapNotNull { value ->
        val key = value.key
        val name = value.name
        if (key != null && name != null) {
            OrionKeyStringItem(id = key, name = name)
        } else null
    }.orEmpty()
)

// This will group all the BOOLEAN fields in one OrionSelectExtended component with multi select enabled.
internal fun AdInsertFieldGroupingDto.toOrionBooleans(): OrionSelectExtended? {
    val booleans = fields.mapNotNull {
        if (it.type != null && it.type == OrionComponentType.BOOLEAN.key) {
            val key = it.id
            val name = it.name
            if (name != null) {
                OrionKeyStringItem(id = key, name = name)
            } else null
        } else null
    }
    return if (booleans.isNotEmpty()) {
        OrionSelectExtended(
            baseData = OrionBaseComponentData(
                id = id.orEmpty(),
                // We will display the title in the group header.
                title = "",
                required = false,
                iconUrl = "",
                validations = listOf()
            ),
            allowMultiSelect = true,
            items = booleans
        )
    } else null
}

internal fun AdInsertFieldDto.toOrionTimePicker() = OrionTimePicker(
    baseData = toOrionBaseComponentData().copy(required = false),
    items = values?.mapNotNull {
        val key = it.key
        val name = it.name
        if (key != null && name != null) OrionKeyStringItem(id = key, name = name) else null
    }.orEmpty()
)

internal fun AdInsertFieldDto.toAccountPhones(account: Account.Connected): OrionBaseComponent? {
    return when (account) {
        is Account.Connected.Private -> account.contact.phone?.let { phone ->
            toOrionTextField(
                enabled = phone.isBlank(),
                isLarge = false,
                inputType = OrionTextField.InputType.PHONE,
                potentialValue = phone
            )
        }

        is Account.Connected.Shop -> {
            val items = account.store.phones.map {
                OrionKeyStringItem(id = it, name = it)
            }
            if (items.isNotEmpty()) {
                OrionNativeDropdown(
                    baseData = toOrionBaseComponentData(),
                    items = items,
                    enabled = false,
                    potentialValue = items.firstOrNull()?.let {
                        OrionKeyStringValue(it.id, it.name)
                    }
                )
            } else null
        }
    }
}

internal fun AdInsertFieldDto.toOrionNativeDropdown(): OrionNativeDropdown {
    val items = values?.mapNotNull {
        val key = it.key
        val name = it.name
        if (key != null && name != null) {
            OrionKeyStringItem(id = key, name = name)
        } else null
    }.orEmpty()
    return OrionNativeDropdown(
        baseData = toOrionBaseComponentData(),
        items = items,
        enabled = false
    )
}

internal fun AdInsertFieldDto.toOrionAlter(type: OrionAlert.Type) = OrionAlert(
    baseData = toOrionBaseComponentData(),
    text = name.orEmpty(),
    type = type
)

internal fun AdInsertFieldDto.toOrionImageUploader(account: Account.Connected): OrionImageUploader {
    val maxAllowedImages = when (account) {
        is Account.Connected.Private -> 8
        is Account.Connected.Shop -> account.store.allowedAccess.adMaxImages
    }
    return OrionImageUploader(
        baseData = toOrionBaseComponentData(),
        maximum = maxAllowedImages
    )
}

internal fun AdInsertFieldDto.toOrionVideoUploader(account: Account.Connected): OrionVideoUploader? {
    return when (account) {
        is Account.Connected.Private -> null
        is Account.Connected.Shop -> OrionVideoUploader(
            baseData = toOrionBaseComponentData(),
            maximum = account.store.allowedAccess.adMaxVideos
        )
    }
}

internal fun AdInsertMediaType.toMediaType() = when (this) {
    AdInsertMediaType.IMAGE -> AdMediaType.IMAGE
    AdInsertMediaType.VIDEO -> AdMediaType.VIDEO
}

internal fun AdInsertFieldDto.toOrionSingleSelectCategoryDropdown() =
    OrionSingleSelectCategoryDropdown(
        baseData = toOrionBaseComponentData(),
        allowParentSelection = false
    )

internal fun AdInsertFieldDto.toOrionVas() = OrionVas(baseData = toOrionBaseComponentData())

internal fun AdInsertFieldDto.toOrionSmartDropdown(account: Account.Connected): OrionSmartDropDown {
    var values = values.orEmpty()
    if (id == Constants.KEY_CITY && account is Account.Connected.Shop && account.store.cities.isNotEmpty()) {
        values = values.filter { value -> account.store.cities.any { it.id == value.key } }
    }
    return OrionSmartDropDown(
        baseData = toOrionBaseComponentData(),
        childBaseData = OrionSmartDropDownChildData(
            id = childParam?.id.orEmpty(),
            title = childParam?.name.orEmpty(),
            required = childParam?.required ?: false
        ),
        items = values.mapNotNull {
            val key = it.key
            val name = it.name
            val item = if (key != null && name != null) OrionSmartDropDownItem(
                id = key,
                name = name,
                trackingName = it.trackingName.orEmpty(),
                children = emptyList()
            ) else return@mapNotNull null
            val children = it.children?.values?.map { child ->
                val childKey = child.key
                val childName = child.name
                if (childKey != null && childName != null) OrionSmartDropDownItem(
                    id = childKey,
                    name = childName,
                    trackingName = child.trackingName.orEmpty(),
                    children = emptyList()
                ) else return@mapNotNull null
            }.orEmpty()
            item.copy(children = children)
        }.sortedByDescending { it.children.size },
        vasAction = when (id) {
            Constants.KEY_CITY -> OrionSmartDropDown.VasAction.REFRESH_VAS_PACKAGES_BY_CITY_AREA
            Constants.KEY_BRAND -> OrionSmartDropDown.VasAction.REFRESH_VAS_PACKAGES_BY_BRAND
            else -> null
        }
    )
}