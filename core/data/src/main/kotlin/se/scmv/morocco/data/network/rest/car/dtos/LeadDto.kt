package se.scmv.morocco.data.network.rest.car.dtos

import com.google.gson.annotations.SerializedName

data class LeadResponseDto(
    val code: Int,
    val req: List<Any>?
)

data class LeadRequestDto(val leads: List<LeadDto>)

data class LeadDto(
    val platform: String,
    val campaign: String,
    val tags: TagsDto
)

data class TagsDto(
    val name: String,
    val phone: String,
    @SerializedName ("option_1") val option1: String,
    @SerializedName ("option_2") val option2: <PERSON><PERSON><PERSON>,
    @SerializedName ("option_3") val option3: String
)