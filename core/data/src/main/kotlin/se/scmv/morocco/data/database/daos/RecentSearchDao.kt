package se.scmv.morocco.data.database.daos

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import kotlinx.coroutines.flow.Flow
import se.scmv.morocco.data.database.entities.RecentSearchEntity

@Dao
interface RecentSearchDao {
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insert(recentSearch: RecentSearchEntity)

    @Query("SELECT * FROM recent_searches ORDER BY created_at DESC")
    fun observeAll(): Flow<List<RecentSearchEntity>>

    @Query("DELETE FROM recent_searches WHERE uuid = :uuid")
    suspend fun delete(uuid: String)
}