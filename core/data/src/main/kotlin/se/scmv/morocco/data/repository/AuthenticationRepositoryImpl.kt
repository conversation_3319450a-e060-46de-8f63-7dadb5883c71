package se.scmv.morocco.data.repository

import com.apollographql.apollo3.ApolloClient
import com.apollographql.apollo3.api.Optional
import com.google.firebase.messaging.FirebaseMessaging
import kotlinx.coroutines.tasks.await
import se.scmv.morocco.CheckAccountExistanceQuery
import se.scmv.morocco.CheckActivationCodeQuery
import se.scmv.morocco.GetMyAccountInfoQuery
import se.scmv.morocco.GetMyStoreInfoQuery
import se.scmv.morocco.LoginByIdentifierMutation
import se.scmv.morocco.LoginWithGoogleMutation
import se.scmv.morocco.LogoutMutation
import se.scmv.morocco.RegisterAccountMutation
import se.scmv.morocco.RegisterFirebaseTokenMutation
import se.scmv.morocco.RequestAccountRecoveryByEmailMutation
import se.scmv.morocco.RequestAccountRecoveryByPhoneMutation
import se.scmv.morocco.RequestPhoneVerificationByPhoneMutation
import se.scmv.morocco.ResetMyPasswordMutation
import se.scmv.morocco.UpdateMyPasswordMutation
import se.scmv.morocco.analytics.AnalyticsHelper
import se.scmv.morocco.analytics.models.AnalyticsAddons
import se.scmv.morocco.analytics.models.AnalyticsEvent
import se.scmv.morocco.analytics.models.Param
import se.scmv.morocco.analytics.models.UserProperties
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.mappers.toAccount
import se.scmv.morocco.data.repository.utils.firstErrorKeyOrNull
import se.scmv.morocco.data.repository.utils.wrapWithErrorHandling
import se.scmv.morocco.data.session.JwtManager
import se.scmv.morocco.data.session.Session
import se.scmv.morocco.data.session.SessionManager
import se.scmv.morocco.data.session.Token
import se.scmv.morocco.domain.models.Account
import se.scmv.morocco.domain.models.AllowedAccess
import se.scmv.morocco.domain.models.NetworkAndBackendErrors
import se.scmv.morocco.domain.models.NetworkErrors
import se.scmv.morocco.domain.models.Resource
import se.scmv.morocco.domain.models.SignInResult
import se.scmv.morocco.domain.models.SignInType
import se.scmv.morocco.domain.models.getOrNull
import se.scmv.morocco.domain.models.networkFailure
import se.scmv.morocco.domain.models.success
import se.scmv.morocco.domain.repositories.AuthenticationRepository
import se.scmv.morocco.type.CheckActivationCodeInput
import se.scmv.morocco.type.DevicePlatform
import se.scmv.morocco.type.FirebaseTokenInput
import se.scmv.morocco.type.MyAccountPhoneInput
import se.scmv.morocco.type.RegisterAccountInput
import se.scmv.morocco.type.RequestAccountRecoveryInput
import se.scmv.morocco.type.RequestPhoneVerificationCodeInput
import se.scmv.morocco.type.ResetMyPasswordInput
import se.scmv.morocco.type.UpdateMyPasswordInput
import javax.inject.Inject

class AuthenticationRepositoryImpl @Inject constructor(
    private val apolloClient: ApolloClient,
    private val sessionManager: SessionManager,
    private val firebaseMessaging: FirebaseMessaging,
    private val analyticsHelper: AnalyticsHelper
) : AuthenticationRepository {

    override suspend fun signIn(
        emailOrPhone: String,
        password: String,
        type: SignInType
    ): Resource<SignInResult, NetworkAndBackendErrors> {
        return loginAndGetAccount(signInType = type) {
            wrapWithErrorHandling(
                call = {
                    val mutation = LoginByIdentifierMutation(emailOrPhone, password)
                    apolloClient.mutation(mutation).execute()
                },
                toData = { apolloResponse ->
                    trackAuthEvent(
                        eventName = AnalyticsEvent.Types.LOGIN_ATTEMPT,
                        authTypeKey = AnalyticsEvent.ParamKeys.LOGIN_TYPE,
                        authTypeValue = type,
                        accountType = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE,
                        errorCode = apolloResponse.firstErrorKeyOrNull()
                    )
                    apolloResponse.data?.loginByIdentifier
                },
                toDomainModel = { data ->
                    Token(accessToken = data.accessToken, refreshToken = data.refreshToken)
                }
            )
        }
    }

    override suspend fun signInWithGoogle(
        idToken: String
    ): Resource<SignInResult, NetworkAndBackendErrors> {
        return loginAndGetAccount(signInType = SignInType.GOOGLE) {
            wrapWithErrorHandling(
                call = {
                    val mutation = LoginWithGoogleMutation(idToken)
                    apolloClient.mutation(mutation).execute()
                },
                toData = { apolloResponse ->
                    trackAuthEvent(
                        eventName = AnalyticsEvent.Types.LOGIN_ATTEMPT,
                        authTypeKey = AnalyticsEvent.ParamKeys.LOGIN_TYPE,
                        authTypeValue = SignInType.GOOGLE,
                        accountType = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE,
                        errorCode = apolloResponse.firstErrorKeyOrNull()
                    )
                    apolloResponse.data?.loginWithGoogle
                },
                toDomainModel = { data ->
                    Token(accessToken = data.accessToken, refreshToken = data.refreshToken)
                }
            )
        }
    }

    override suspend fun sendSmsVerificationForSignUp(
        phoneNumber: String
    ): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = RequestPhoneVerificationCodeInput(
                    phone = phoneNumber,
                    isUserSigningUp = Optional.present(true)
                )
                apolloClient.mutation(RequestPhoneVerificationByPhoneMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.requestPhoneVerification },
            toDomainModel = { data -> data.phone }
        )
    }

    override suspend fun sendSmsVerificationForPasswordReset(phoneNumber: String): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val mutation = RequestAccountRecoveryByPhoneMutation(phoneNumber)
                apolloClient.mutation(mutation).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.requestAccountRecoveryByPhone },
            toDomainModel = { data -> data.phone }
        )
    }

    override suspend fun validatePhone(
        phoneNumber: String?,
        code: String
    ): Resource<Boolean, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = CheckActivationCodeInput(
                    code = code,
                    phone = Optional.presentIfNotNull(phoneNumber)
                )
                apolloClient.query(CheckActivationCodeQuery(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.checkActivationCode },
            toDomainModel = { data -> data.isValid }
        )
    }

    override suspend fun sendPasswordResetLink(email: String): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = RequestAccountRecoveryInput(email = email)
                apolloClient.mutation(RequestAccountRecoveryByEmailMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.requestAccountRecovery },
            toDomainModel = { data -> data.email }
        )
    }

    override suspend fun registerAccount(
        phoneNumber: String,
        otpCode: String?,
        fullName: String,
        password: String,
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = RegisterAccountInput(
                    name = Optional.present(fullName),
                    email = Optional.absent(),
                    phone = MyAccountPhoneInput(number = phoneNumber, isHidden = false),
                    location = Optional.absent(),
                    password = password,
                    phoneVerificationCode = Optional.presentIfNotNull(otpCode)
                )
                apolloClient.mutation(RegisterAccountMutation(input)).execute()
            },
            toData = { apolloResponse ->
                trackAuthEvent(
                    eventName = AnalyticsEvent.Types.SIGUP_ATTEMPT,
                    authTypeKey = AnalyticsEvent.ParamKeys.SIGNUP_TYPE,
                    authTypeValue = SignInType.EMAIL,
                    accountType = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE,
                    errorCode = apolloResponse.firstErrorKeyOrNull()
                )
                // TODO check with the Backend why the login is nullable !
                val login = apolloResponse.data?.registerAccount?.login
                val info = apolloResponse.data?.registerAccount?.info
                when {
                    login != null && info != null -> login to info
                    else -> null
                }
            },
            toDomainModel = { data ->
                val token = with(data.first) { Token(accessToken, refreshToken) }
                val account = data.second.toAccount()
                sessionManager.createSession(token = token, account = account)
                val messagingToken = registerFirebaseToken()
                trackAuthEvent(
                    eventName = AnalyticsEvent.Types.SIGNED_UP,
                    authTypeKey = AnalyticsEvent.ParamKeys.SIGNUP_TYPE,
                    account = account,
                    accountType = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE,
                    authTypeValue = SignInType.EMAIL,
                    messagingToken = messagingToken.getOrNull()
                )
            }
        )
    }

    override suspend fun resetPassword(
        code: String,
        newPassword: String
    ): Resource<String, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = ResetMyPasswordInput(code = code, newPassword = newPassword)
                apolloClient.mutation(ResetMyPasswordMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.resetMyPassword },
            toDomainModel = { data -> data.code }
        )
    }

    override suspend fun updatePassword(
        currentPassword: String,
        newPassword: String
    ): Resource<Unit, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val input = UpdateMyPasswordInput(
                    currentPassword = Optional.present(currentPassword),
                    newPassword = newPassword
                )
                apolloClient.mutation(UpdateMyPasswordMutation(input)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.updateMyPassword },
            toDomainModel = { }
        )
    }

    override suspend fun signOut(): Resource<Boolean, NetworkAndBackendErrors> {
        // Unregister the firebase token before logging out, if it fails, we still continue
        unregisterFirebaseToken()
        return wrapWithErrorHandling(
            call = { apolloClient.mutation(LogoutMutation()).execute() },
            toData = { apolloResponse -> apolloResponse.data?.logout },
            toDomainModel = { loggedOut ->
                if (loggedOut) {
                    sessionManager.endSession()
                }
                loggedOut
            }
        )
    }

    override suspend fun checkAccountExistance(phone: String): Resource<Boolean, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val query = CheckAccountExistanceQuery(phone)
                apolloClient.query(query).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.checkAccountExistance },
            toDomainModel = { data -> data.doesAccountExist }
        )
    }

    private suspend inline fun loginAndGetAccount(
        signInType: SignInType,
        login: () -> Resource<Token, NetworkAndBackendErrors>,
    ): Resource<SignInResult, NetworkAndBackendErrors> {
        return when (val loginResult = login()) {
            is Resource.Success -> {
                // Check for ecommerce store owner role in the token
                if (JwtManager.isEcommerceStoreOwner(loginResult.data.accessToken)) {
                    // Return a special error for ecommerce store owners
                    return success(SignInResult.ECOMMERCE_RESTRICTION)
                }
                val session = JwtManager.mapToSession(loginResult.data.accessToken, 1)
                when (session) {
                    is Session.Private -> {
                        // Save the accessToken in prefs so that [AuthorizationInterceptor] adds the Bearer token.
                        sessionManager.setAccessToken(loginResult.data.accessToken)
                        // Register the firebase token, if it fails, we still continue
                        val messagingToken = registerFirebaseToken()
                        when (val result = getPrivateAccount()) {
                            is Resource.Success -> {
                                sessionManager.createSession(
                                    token = Token(
                                        accessToken = loginResult.data.accessToken,
                                        refreshToken = loginResult.data.refreshToken
                                    ),
                                    account = result.data
                                )
                                trackAuthEvent(
                                    eventName = AnalyticsEvent.Types.LOGGED_IN,
                                    authTypeKey = AnalyticsEvent.ParamKeys.LOGIN_TYPE,
                                    authTypeValue = signInType,
                                    accountType = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_PRIVATE,
                                    account = result.data,
                                    messagingToken = messagingToken.getOrNull()
                                )
                                success(SignInResult.SUCCESS)
                            }

                            is Resource.Failure -> {
                                sessionManager.endSession()
                                result
                            }
                        }
                    }

                    is Session.Shop -> {
                        // Save the accessToken in prefs so that [AuthorizationInterceptor] adds the Bearer token.
                        sessionManager.setAccessToken(loginResult.data.accessToken)
                        // Register the firebase token, if it fails, we still continue
                        val messagingToken = registerFirebaseToken()
                        when (
                            val result = getShopAccount(
                                storeId = session.sessionInfo.accountId.toString(),
                                allowedAccess = session.allowedAccess
                            )
                        ) {
                            is Resource.Success -> {
                                sessionManager.createSession(
                                    token = Token(
                                        accessToken = loginResult.data.accessToken,
                                        refreshToken = loginResult.data.refreshToken
                                    ),
                                    account = result.data
                                )
                                trackAuthEvent(
                                    eventName = AnalyticsEvent.Types.LOGGED_IN,
                                    authTypeKey = AnalyticsEvent.ParamKeys.LOGIN_TYPE,
                                    authTypeValue = signInType,
                                    accountType = AnalyticsEvent.ParamValues.ACCOUNT_TYPE_SHOP,
                                    account = result.data,
                                    messagingToken = messagingToken.getOrNull()
                                )
                                success(SignInResult.SUCCESS)
                            }

                            is Resource.Failure -> {
                                sessionManager.endSession()
                                result
                            }
                        }
                    }

                    Session.NotLogged -> networkFailure(NetworkErrors.UNKNOWN)
                }
            }

            is Resource.Failure -> loginResult
        }
    }

    private suspend fun getPrivateAccount(): Resource<Account.Connected.Private, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(GetMyAccountInfoQuery()).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getMyAccountInfo },
            toDomainModel = { accountData -> accountData.toAccount() }
        )
    }

    private suspend fun getShopAccount(
        storeId: String,
        allowedAccess: AllowedAccess
    ): Resource<Account.Connected.Shop, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                apolloClient.query(GetMyStoreInfoQuery(storeId)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.getMyStoreInfo },
            toDomainModel = { accountData -> accountData.toAccount(storeId, allowedAccess) }
        )
    }

    private suspend fun registerFirebaseToken(): Resource<String, NetworkAndBackendErrors> {
        val messagingToken = try {
            firebaseMessaging.token.await()
        } catch (_: Exception) {
            return networkFailure(NetworkErrors.UNKNOWN)
        }
        return wrapWithErrorHandling(
            call = {
                val token = FirebaseTokenInput(
                    platform = DevicePlatform.ANDROID,
                    new = Optional.present(listOf(messagingToken)),
                    revoked = Optional.Absent
                )
                apolloClient.mutation(RegisterFirebaseTokenMutation(token)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.registerFirebaseToken },
            toDomainModel = { data ->
                if (data.success) {
                    messagingToken
                } else {
                    ""
                }
            }
        )
    }

    private suspend fun unregisterFirebaseToken(): Resource<Boolean, NetworkAndBackendErrors> {
        return wrapWithErrorHandling(
            call = {
                val messagingToken = firebaseMessaging.token.await()
                val token = FirebaseTokenInput(
                    platform = DevicePlatform.ANDROID,
                    new = Optional.Absent,
                    revoked = Optional.presentIfNotNull(listOf(messagingToken))
                )
                apolloClient.mutation(RegisterFirebaseTokenMutation(token)).execute()
            },
            toData = { apolloResponse -> apolloResponse.data?.registerFirebaseToken },
            toDomainModel = { data -> data.success }
        )
    }

    private fun trackAuthEvent(
        eventName: String,
        authTypeKey: String,
        authTypeValue: SignInType,
        accountType: String,
        account: Account.Connected? = null,
        messagingToken: String? = null,
        errorCode: String? = null,
    ) {
        account?.let {
            val userProperties = with(it.connectedContact()) {
                UserProperties(
                    name = name,
                    phone = phone.orEmpty(),
                    email = email
                )
            }
            analyticsHelper.identify(
                accountId = it.connectedContact().accountId,
                properties = userProperties,
                messagingToken = messagingToken
            )
        }
        analyticsHelper.logEvent(
            event = AnalyticsEvent(
                name = eventName,
                properties = mutableSetOf(
                    Param(
                        key = AnalyticsEvent.ParamKeys.LANG,
                        value = LocaleManager.getCurrentLanguage()
                    ),
                    Param(
                        key = AnalyticsEvent.ParamKeys.ACCOUNT_TYPE,
                        value = accountType
                    ),
                    Param(
                        key = authTypeKey,
                        value = authTypeValue.name.lowercase()
                    ),
                ).apply {
                    if (errorCode != null) {
                        add(Param(AnalyticsEvent.ParamKeys.ERROR_TYPE, errorCode))
                    }
                }
            )
        )
        val brazeEvent = when (authTypeValue) {
            SignInType.EMAIL, SignInType.PHONE -> AnalyticsEvent.Types.LOG_IN
            SignInType.GOOGLE -> AnalyticsEvent.Types.LOG_IN_GOOGLE
        }
        analyticsHelper.logEvent(
            event = AnalyticsEvent(name = brazeEvent),
            where = setOf(AnalyticsAddons.BRAZE)
        )
    }
}