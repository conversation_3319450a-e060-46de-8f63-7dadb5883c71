package se.scmv.morocco.data.network.rest.config.dtos

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class AdInsertCategoryDto(
    @SerializedName("children") var children: List<AdInsertCategoryDto>? = null,
    @SerializedName("icon") var icon: String? = null,
    @SerializedName("index") var index: Int,
    @SerializedName("category") var category: CategoryTreeDto,
    @SerializedName("name") var name: String? = null,
    @SerializedName("trackingName") var trackingName: String? = null,
    @SerializedName("adTypes") var adTypes: List<AdTypesDto>? = null
)

@Keep
data class AdTypesDto(
    @SerializedName("key") var key: String,
    @SerializedName("name") var name: String,
    @SerializedName("trackingName") var trackingName: String
)

@Keep
data class CategoryTreeDto(@SerializedName("id") var id: String)
