package se.scmv.morocco.data.network.rest.car

import retrofit2.Response
import retrofit2.http.Body
import retrofit2.http.POST
import retrofit2.http.Query
import se.scmv.morocco.data.network.rest.car.dtos.LeadRequestDto
import se.scmv.morocco.data.network.rest.car.dtos.LeadResponseDto

interface CarCheckApi {
    @POST("ws/leads/store")
    suspend fun sendLead(
        @Query("reseller") reseller: String,
        @Body leadRequest: LeadRequestDto
    ): Response<LeadResponseDto>
}