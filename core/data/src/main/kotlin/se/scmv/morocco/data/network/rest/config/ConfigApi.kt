package se.scmv.morocco.data.network.rest.config

import retrofit2.http.GET
import retrofit2.http.Query
import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.network.rest.config.dtos.AdInsertCategoryDto
import se.scmv.morocco.data.network.rest.config.dtos.AdInsertStepsDto
import se.scmv.morocco.data.network.rest.config.dtos.ListingCategoryFiltersDto
import se.scmv.morocco.data.network.rest.config.dtos.ListingFilterCategoriesDto

/**
 * Interface for interacting with the Config API.
 * Provides methods for retrieving ad insertion steps, categories, and filters for listings.
 */
interface ConfigApi {
    /**
     * Fetches the steps required for ad insertion.
     *
     * @param categoryId The ID of the category for which to fetch the ad insert steps.
     * @param typeId The type of advertisement (as a string identifier).
     * @param lang The language in which the data should be retrieved. Defaults to the current language set by `LocaleManager`.
     * @return [AdInsertStepsDto] containing the ad insertion steps.
     */
    @GET("adinsert/steps")
    suspend fun getAdInsertSteps(
        @Query("category_id") categoryId: Int,
        @Query("type") typeId: String,
        @Query("lang") lang: String = LocaleManager.getCurrentLanguage()
    ): AdInsertStepsDto

    /**
     * Retrieves the available categories for ad insertion.
     *
     * @param lang The language in which the categories should be retrieved. Defaults to the current language set by `LocaleManager`.
     * @return An array of [AdInsertCategoryDto] objects representing the available categories.
     */
    @GET("adinsert/tree")
    suspend fun getAdInsertCategories(
        @Query("lang") lang: String = LocaleManager.getCurrentLanguage()
    ): List<AdInsertCategoryDto>

    /**
     * Retrieves the available categories for ad listing filters.
     *
     * @param lang The language in which the categories should be retrieved. Defaults to the current language set by `LocaleManager`.
     * @return An array of [ListingFilterCategoriesDto] objects representing the available categories.
     */
    @GET("adlisting/tree")
    suspend fun getFiltersCategories(
        @Query("lang") lang: String = LocaleManager.getCurrentLanguage()
    ): List<ListingFilterCategoriesDto>

    /**
     * Fetches the filters applicable to listing.
     *
     * @param categoryId The ID of the category for which to fetch the ad insert steps.
     * @param typeId The type of advertisement (as a string identifier).
     * @param lang The language in which the filters should be retrieved. Defaults to the current language set by `LocaleManager`.
     * @return An array of [ListingCategoryFiltersDto] objects representing the filters.
     */
    @GET("adlisting/filters")
    suspend fun getFilters(
        @Query("category_id") categoryId: Int,
        @Query("type") typeId: String,
        @Query("lang") lang: String = LocaleManager.getCurrentLanguage()
    ): ListingCategoryFiltersDto
}