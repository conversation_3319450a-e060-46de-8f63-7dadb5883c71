package se.scmv.morocco.data.network.rest.tp.dtos

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName
import se.scmv.morocco.domain.models.AdTouchingPoint
import se.scmv.morocco.domain.models.CampaignData
import se.scmv.morocco.domain.models.LabelValue
import se.scmv.morocco.domain.models.Targeting
import java.util.UUID


@Keep
data class TouchingPointDto(
    @SerializedName("ads") val ads: List<Ad>
) {
    @Keep
    data class Ad(
        @SerializedName("_id") val id: String? = null,
        @SerializedName("isInactive") val inactive: Boolean? = null,
        @SerializedName("type") val type: String? = null,
        @SerializedName("campaignData") val campaignData: TpCampaignData? = null,
        @SerializedName("name") val name: String? = null,
        @SerializedName("client") val client: String? = null,
        @SerializedName("platforms") val platforms: List<String>? = null,
        @SerializedName("targeting") val targeting: TouchingPointTargeting? = null,
        @SerializedName("dateCreated") val dateCreated: String? = null,
    )

    @Keep
    data class TpCampaignData(
        @SerializedName("adId") var adId: String? = null,
        @SerializedName("sov") var sov: List<String>? = null,
        @SerializedName("message") val title: String? = null,
        @SerializedName("message2") val description: String? = null,
        @SerializedName("clientLogo") val clientLogo: String? = null,
        @SerializedName("redirectLink") val redirectLink: String? = null,
    )

    @Keep
    data class TouchingPointTargeting(
        @SerializedName("categories") val categories: List<TPCategory> = emptyList(),
        @SerializedName("cities") val cities: List<TPCities> = emptyList(),
        @SerializedName("priceMin") val priceMin: Int? = null,
        @SerializedName("priceMax") val priceMax: Int? = null,
    )

    @Keep
    data class TPCategory(
        @SerializedName("value") val value: String? = null,
        @SerializedName("label") var label: String? = null
    )

    @Keep
    data class TPCities(
        @SerializedName("value") val value: String? = null,
        @SerializedName("label") val label: String? = null
    )
}

fun TouchingPointDto.Ad.toAdTouchingPoint() = AdTouchingPoint(
    id = id ?: UUID.randomUUID().toString(),
    campaignData = CampaignData(
        shareOfVoice = campaignData?.sov.orEmpty(),
        title = campaignData?.title.orEmpty(),
        description = campaignData?.description.orEmpty(),
        clientLogo = campaignData?.clientLogo.orEmpty(),
        redirectLink = campaignData?.redirectLink.orEmpty()
    ),
    platforms = platforms.orEmpty(),
    targeting = Targeting(
        categories = targeting?.categories?.map {
            LabelValue(id = it.value.orEmpty(), label = it.label.orEmpty())
        }.orEmpty(),
        cities = targeting?.cities?.map {
            LabelValue(id = it.value.orEmpty(), label = it.label.orEmpty())
        }.orEmpty()
    )
)
