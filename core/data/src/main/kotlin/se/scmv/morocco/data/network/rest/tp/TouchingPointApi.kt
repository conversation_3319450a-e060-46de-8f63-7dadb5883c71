package se.scmv.morocco.data.network.rest.tp

import retrofit2.Response
import retrofit2.http.GET
import retrofit2.http.Path
import retrofit2.http.Query
import se.scmv.morocco.data.network.rest.tp.dtos.TouchingPointDto

interface TouchingPointApi {
    @GET("campaigns")
    suspend fun fetchTouchingPoints(
        @Query("type") type: String = "tp",
        @Query("active") active: Boolean = true
    ): Response<TouchingPointDto>

    @GET("campaigns/{campaignId}/stats/{clickOrImpression}")
    suspend fun recordTouchingPoint(
        @Path("campaignId") campaignId: String,
        @Path("clickOrImpression") clickOrImpression: String,
    ): Response<TouchingPointDto>
}