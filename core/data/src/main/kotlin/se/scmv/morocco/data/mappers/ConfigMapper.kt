package se.scmv.morocco.data.mappers

import se.scmv.morocco.common.lang.LocaleManager
import se.scmv.morocco.data.database.entities.CityEntity
import se.scmv.morocco.data.database.entities.CityWithTownsEntity
import se.scmv.morocco.data.database.entities.TownEntity
import se.scmv.morocco.data.network.rest.car.dtos.CarCheckedDto
import se.scmv.morocco.data.network.rest.car.dtos.CarToCheckDto
import se.scmv.morocco.data.network.rest.car.dtos.InspectionRequestFormDto
import se.scmv.morocco.data.network.rest.config.dtos.AdInsertCategoryDto
import se.scmv.morocco.data.network.rest.config.dtos.BankAppDto
import se.scmv.morocco.data.network.rest.config.dtos.ListingFilterCategoriesDto
import se.scmv.morocco.data.repository.utils.buildCategoryIconUrl
import se.scmv.morocco.domain.models.AdType
import se.scmv.morocco.domain.models.AdTypeKey
import se.scmv.morocco.domain.models.Area
import se.scmv.morocco.domain.models.BankApp
import se.scmv.morocco.domain.models.CarChecked
import se.scmv.morocco.domain.models.CarCheckedLocaleContent
import se.scmv.morocco.domain.models.CarToCheck
import se.scmv.morocco.domain.models.Category
import se.scmv.morocco.domain.models.CategoryTree
import se.scmv.morocco.domain.models.City
import se.scmv.morocco.domain.models.CtaButton
import se.scmv.morocco.domain.models.InspectionRequestForm
import se.scmv.morocco.domain.models.LocaleContent
import se.scmv.morocco.domain.models.LocaleFormContent
import se.scmv.morocco.domain.models.filter.ListingFilterCategories

// FILTERS
fun ListingFilterCategoriesDto.toListingFilterCategories(): ListingFilterCategories{
    return ListingFilterCategories(
        category = ListingFilterCategories.Category(
            adType = category.adType,
            id = category.id
        ),
        icon = icon.toBuildIconUrl(),
        index = index,
        name = name ?: "",
        trackingName = trackingName,
        urlSlug = urlSlug,
        children = children?.toListListingFilterCategories()
    )
}

fun List<ListingFilterCategoriesDto>.toListListingFilterCategories(): List<ListingFilterCategories>{
    return map {
        it.toListingFilterCategories()
    }
}

fun String.toBuildIconUrl(): String{
    return let {
        "https://assets.avito.ma/icons/svg/$this.svg"
    }
}

fun ListingFilterCategoriesDto.toCategory() = Category(
    id = category.id,
    name = name.orEmpty(),
    trackingName = trackingName.orEmpty(),
    icon = buildCategoryIconUrl(category.id)
)

fun ListingFilterCategoriesDto.toCategoryTree(parent: Category? = null) :CategoryTree? {
    // TODO This should be stored in firebase remote config
    if (category.id == "7900") return null
    val extractedCategory = toCategory(parent)
    return CategoryTree(
        category = extractedCategory,
        adTypes = listOf(
            AdType(
                key = AdTypeKey.valueOf(category.adType.uppercase()),
                name = category.adType,
                trackingName = category.adType
            )
        ),
        children = children?.mapNotNull { child ->
            child.toCategoryTree(parent = extractedCategory)
        }.orEmpty()
    )
}

fun ListingFilterCategoriesDto.toCategory(parent: Category? = null) = Category(
    id = category.id,
    name = name.orEmpty(),
    trackingName = trackingName.orEmpty(),
    icon = buildCategoryIconUrl(category.id),
    parent = parent
)

// AD INSERT
fun AdInsertCategoryDto.toCategory(parent: Category? = null) = Category(
    id = category.id,
    name = name.orEmpty(),
    icon = buildCategoryIconUrl(category.id),
    trackingName = trackingName.orEmpty(),
    parent = parent
)

fun AdInsertCategoryDto.toCategoryTree(parent: Category? = null): CategoryTree? {
    // TODO This should be stored in firebase remote config
    if (category.id == "7900") return null
    val extractedCategory = toCategory(parent)
    return CategoryTree(
        category = extractedCategory,
        adTypes = adTypes?.map {
            AdType(
                key = AdTypeKey.valueOf(it.key.uppercase()),
                name = it.name,
                trackingName = it.trackingName
            )
        }.orEmpty(),
        children = children?.mapNotNull { child ->
            child.toCategoryTree(parent = extractedCategory)
        }.orEmpty()
    )
}

// VAS
fun BankAppDto.toBankApp() = BankApp(frLabel = frLabel, arLabel = arLabel, iconUrl = iconUrl)

// CITY
fun CityEntity.toCity() = City(
    id = id,
    name = if (LocaleManager.isAr()) nameAr else nameFr,
    trackingName = trackingName
)

fun TownEntity.toArea() = Area(
    id = id,
    name = if (LocaleManager.isAr()) nameAr else nameFr,
    trackingName = trackingName
)

fun CityWithTownsEntity.toCityWithArea(areaId: String?) = City(
    id = city.id,
    name = if (LocaleManager.isAr()) city.nameAr else city.nameFr,
    trackingName = city.trackingName,
    area = towns.firstOrNull { it.id == areaId }?.toArea()
)

// CAR CHECK
fun CarToCheckDto.toCarToCheck(): CarToCheck {
    return CarToCheck(
        locales = locales.mapValues { (_, locale) ->
            LocaleContent(
                title = locale.title,
                description = locale.description,
                tags = locale.tags,
                buttonText = locale.buttonText
            )
        },
        inspectionRequestForm = inspectionRequestForm.toInspectionRequestForm()
    )
}

fun CarCheckedDto.toCarChecked(): CarChecked {
    return CarChecked(
        locales = locales.mapValues { (_, locale) ->
            CarCheckedLocaleContent(
                title = locale.title,
                description = locale.description,
                tags = locale.tags,
                buttonText = locale.buttonText,
                dateLabel = locale.dateLabel,
                badgeText = locale.badgeText,
                reportButtonText = locale.reportButtonText,
                downloadButtonText = locale.downloadButtonText,
                redirectUrl = locale.redirectUrl
            )
        }
    )
}

fun InspectionRequestFormDto.toInspectionRequestForm(): InspectionRequestForm {
    return InspectionRequestForm(
        locales = locales.mapValues { (_, locale) ->
            LocaleFormContent(
                title = locale.title,
                serviceAvailableCities = locale.serviceAvailableCities,
                ctaButton = CtaButton(
                    text = locale.ctaButton.text,
                    redirectUrl = locale.ctaButton.redirectUrl
                )
            )
        }
    )
}