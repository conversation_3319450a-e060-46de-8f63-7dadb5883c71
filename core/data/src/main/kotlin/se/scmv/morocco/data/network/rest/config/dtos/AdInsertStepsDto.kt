package se.scmv.morocco.data.network.rest.config.dtos

import androidx.annotation.Keep
import com.google.gson.annotations.SerializedName

@Keep
data class AdInsertStepsDto(
    val category: Category,
    var steps: List<AdInsertStepDto> = emptyList()
) {
    @Keep
    data class Category(
        val id: String,
        val adType: String
    )
}

@Keep
data class AdInsertStepDto(
    @SerializedName("fieldGrouping") val fieldGrouping: List<AdInsertFieldGroupingDto> = arrayListOf(),
    @SerializedName("stepID") val stepID: String? = null,
    @SerializedName("title") val title: String? = null,
    @SerializedName("subtitle") val subtitle: String? = null,
    @SerializedName("icon") val icon: String? = null,
    @SerializedName("tipsTitle") val tipsTitle: String? = null,
    @SerializedName("tips") val tips: List<String>? = null,
    @SerializedName("infoLink") val infoLink: AdInsertStepInfoLink? = null
)

@Keep
data class AdInsertFieldGroupingDto(
    @SerializedName("id") val id: String? = null,
    @SerializedName("title") val title: String? = null,
    @SerializedName("subtitle") val subtitle: String? = null,
    @SerializedName("fields") val fields: List<AdInsertFieldDto> = emptyList()
)

@Keep
data class AdInsertFieldDto(
    @SerializedName("id") val id: String,
    @SerializedName("isParam") val isParam: Boolean? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("position") val position: String? = null,
    @SerializedName("icon") val icon: String? = null,
    @SerializedName("required") val required: Boolean? = null,
    @SerializedName("type") val type: String? = null,
    @SerializedName("values") val values: List<AdInsertValuesDto>? = null,
    @SerializedName("suffix") val suffix: String? = null,
    @SerializedName("validation") val validation: List<AdInsertValidationDto>? = null,
    @SerializedName("range") val range: List<String>? = null,
    @SerializedName("childParam") val childParam: AdInsertChildParamDto?
)

@Keep
data class AdInsertValuesDto(
    @SerializedName("key") val key: String? = null,
    @SerializedName("name") val name: String? = null,
    @SerializedName("trackingName") val trackingName: String? = null,
    @SerializedName("icon") val icon: String? = null,
    @SerializedName("children") val children: AdInsertChildrenDto? = null
)

@Keep
data class AdInsertChildrenDto(
    @SerializedName("values") val values: List<AdInsertValuesDto>? = null
)

@Keep
data class AdInsertValidationDto(
    @SerializedName("errorMessage") val errorMessage: String?,
    @SerializedName("regex") val regex: String?
)

@Keep
data class AdInsertChildParamDto(
    @SerializedName("id") val id: String?,
    @SerializedName("name") val name: String?,
    @SerializedName("required") val required: Boolean?
)

@Keep
data class AdInsertStepInfoLink (
    val url: String? = null,
    val text: String? = null
)