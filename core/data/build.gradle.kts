plugins {
    id("avito.android.library")
    id("avito.android.hilt")
    alias(libs.plugins.apollo3)
    alias(libs.plugins.room)
}

android {
    namespace = "se.scmv.morocco.data"

    buildFeatures {
        buildConfig = true
    }

    flavorDimensions += "environment"
    productFlavors {
        create("prod") {
            dimension = "environment"
            buildConfigField("String", "GATEWAY_GRAPHQL_URL", "\"https://gateway.avito.ma/graphql\"")
            buildConfigField("String", "GATEWAY_WEBSOCKET_URL", "\"https://gateway.avito.ma/websocket\"")
            buildConfigField("String", "CONFIG_API_URL", "\"https://services.avito.ma/api/v2/config/\"")
            buildConfigField("String", "CONFIG_API_V1_URL", "\"https://services.avito.ma/api/v1/\"")
            buildConfigField("String", "LEADS_FORCE_API_URL", "\"https://api.leadsforce.ma/\"")
            buildConfigField("String", "MEDIA_SERVER_API_URL", "\"https://media-server.avito.ma/\"")
        }
        create("pre") {
            dimension = "environment"
            buildConfigField("String", "GATEWAY_GRAPHQL_URL", "\"https://gateway-pre.avito.ma/graphql\"")
            buildConfigField("String", "GATEWAY_WEBSOCKET_URL", "\"https://gateway-pre.avito.ma/websocket\"")
            buildConfigField("String", "CONFIG_API_URL", "\"https://services-pre.avito.ma/api/v2/config/\"")
            buildConfigField("String", "CONFIG_API_V1_URL", "\"https://services-pre.avito.ma/api/v1/\"")
            buildConfigField("String", "LEADS_FORCE_API_URL", "\"https://api-pre.leadsforce.ma/\"")
            buildConfigField("String", "MEDIA_SERVER_API_URL", "\"https://media-server-pre.avito.ma/\"")
        }
    }
}

dependencies {
    implementation(project(":core:domain"))
    implementation(project(":core:common"))
    implementation(project(":core:datastore"))
    implementation(project(":core:analytics"))

    // TODO Upgrade to apollo 4.
    // Apollo
    implementation(libs.apollo3.runtime)
    implementation(libs.apollo3.normalized.cache)

    // Retrofit
    implementation(libs.com.squareup.retrofit2.retrofit)
    implementation(libs.com.squareup.retrofit2.retrofit.mock)
    implementation(libs.com.squareup.retrofit2.converter.gson)
    implementation(libs.com.squareup.retrofit2.adapter.rxjava2)

    // Okhttp
    implementation(platform(libs.com.squareup.okhttp3.okhttpBom))
    implementation(libs.com.squareup.okhttp3.okhttp)
    implementation(libs.com.squareup.okhttp3.logging.interceptor)
    implementation(libs.com.squareup.okhttp3.mockwebserver)

    // Room
    implementation(libs.androidx.room.runtime)
    implementation(libs.androidx.room.ktx)
    ksp(libs.androidx.room.compiler)

    // Paging
    implementation(libs.androidx.paging)

    // Compressor
    implementation(libs.compressor)

    // Firebase
    implementation(platform(libs.com.google.firebase.bom))
    implementation(libs.com.google.firebase.config)
    implementation(libs.com.google.firebase.config.ktx)
    implementation(libs.com.google.firebase.messaging)
}

apollo {
    service("avito") {
        packageName.set("se.scmv.morocco")
        generateApolloMetadata.set(true)
        schemaFile.set(File("src/main/graphql/se/scmv/morocco/schema.graphqls"))
        srcDir(file("src/main/graphql/"))
        // Enable generation of metadata for use by downstream modules
        mapScalarToUpload("Upload")
        generateDataBuilders.set(true)
    }
}

room {
    schemaDirectory("$projectDir/schemas")
}