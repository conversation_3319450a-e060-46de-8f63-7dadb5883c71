package se.scmv.morocco.designsystem.utils

import android.Manifest
import android.content.Context
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Build
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.core.net.toUri
import se.scmv.morocco.designsystem.R

/**
 * Utility functions for handling contact actions (WhatsApp, Phone Call, Chat)
 */
object ContactActionUtils {
    
    /**
     * Checks if WhatsApp is installed on the device
     * Supports both regular WhatsApp and WhatsApp Business
     */
    fun isWhatsAppInstalled(context: Context): Boolean {
        val pm = context.packageManager

        fun isInstalled(packageName: String): <PERSON><PERSON>an {
            return try {
                if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
                    pm.getPackageInfo(packageName, PackageManager.PackageInfoFlags.of(0))
                } else {
                    @Suppress("DEPRECATION")
                    pm.getPackageInfo(packageName, 0)
                }
                true
            } catch (_: PackageManager.NameNotFoundException) {
                false
            }
        }

        // Check both regular WhatsApp and WhatsApp Business
        return isInstalled("com.whatsapp") || isInstalled("com.whatsapp.w4b")
    }
    
    /**
     * Launches WhatsApp with a pre-filled message
     * Handles both regular WhatsApp and WhatsApp Business
     */
    fun launchWhatsApp(context: Context, phoneNumber: String) {
        try {
            val message = context.getString(R.string.chat_pre_message)
            val phone = "212${phoneNumber.substring(1)}"
            val intent = Intent(Intent.ACTION_VIEW).apply {
                data = "https://api.whatsapp.com/send?phone=$phone&text=${Uri.encode(message)}".toUri()
                setPackage("com.whatsapp") // Try regular WhatsApp first
            }
            
            if (intent.resolveActivity(context.packageManager) != null) {
                context.startActivity(intent)
            } else {
                // Try WhatsApp Business
                intent.setPackage("com.whatsapp.w4b")
                if (intent.resolveActivity(context.packageManager) != null) {
                    context.startActivity(intent)
                } else {
                    showToast(context, context.getString(R.string.contact_action_error_opening_whatsapp))
                }
            }
        } catch (e: Exception) {
            showToast(context, context.getString(R.string.contact_action_error_opening_whatsapp))
        }
    }
    
    /**
     * Launches phone call application
     * Handles permissions and falls back to dialer if needed
     */
    fun launchPhoneCall(context: Context, phoneNumber: String) {
        try {
            // Check for CALL_PHONE permission
            if (ContextCompat.checkSelfPermission(context, Manifest.permission.CALL_PHONE) 
                == PackageManager.PERMISSION_GRANTED) {
                val intent = Intent(Intent.ACTION_CALL).apply {
                    data = Uri.parse("tel:$phoneNumber")
                }
                context.startActivity(intent)
            } else {
                // Fallback to dialer
                val dialIntent = Intent(Intent.ACTION_DIAL).apply {
                    data = Uri.parse("tel:$phoneNumber")
                }
                context.startActivity(dialIntent)
            }
        } catch (e: Exception) {
            showToast(context, context.getString(R.string.contact_action_error_phone_call))
        }
    }
    
    /**
     * Shows a toast message
     */
    fun showToast(context: Context, message: String) {
        Toast.makeText(context, message, Toast.LENGTH_LONG).show()
    }
    
    /**
     * Handles WhatsApp CTA with installation validation
     */
    fun handleWhatsAppCTA(context: Context, phoneNumber: String) {
        if (isWhatsAppInstalled(context)) {
            launchWhatsApp(context, phoneNumber)
        } else {
            showToast(context, context.getString(R.string.not_found_whatssapp_message))
        }
    }
    
    /**
     * Handles chat CTA by calling the provided callback function
     * The callback should handle authentication check and show FirstMessageBottomSheet
     */
    fun handleChatCTA(context: Context, adId: String, onChatAction: (String) -> Unit) {
        onChatAction(adId)
    }
    
    fun handleWhatsAppAction(context: Context, phoneNumber: String?) {
        if (phoneNumber.isNullOrBlank()) return
        
        if (isWhatsAppInstalled(context)) {
            val formattedNumber = formatPhoneForWhatsApp(phoneNumber)
            launchWhatsApp(context, formattedNumber)
        } else {
            showToast(context, context.getString(R.string.not_found_whatssapp_message))
        }
    }
    
    fun handlePhoneCallAction(context: Context, phoneNumber: String?) {
        if (phoneNumber.isNullOrBlank()) return
        
        val formattedNumber = formatPhoneForCall(phoneNumber)
        launchPhoneCall(context, formattedNumber)
    }
    
    private fun formatPhoneForWhatsApp(phoneNumber: String): String {
        val cleanNumber = phoneNumber.trim()
        
        return if (cleanNumber.startsWith("+")) {
            cleanNumber.substring(1)
        } else {
            val numberWithoutZero = if (cleanNumber.startsWith("0")) {
                cleanNumber.substring(1)
            } else {
                cleanNumber
            }
            "212$numberWithoutZero"
        }
    }
    
    private fun formatPhoneForCall(phoneNumber: String): String {
        val cleanNumber = phoneNumber.trim()
        
        return if (cleanNumber.startsWith("+")) {
            cleanNumber
        } else {
            val numberWithoutZero = if (cleanNumber.startsWith("0")) {
                cleanNumber.substring(1)
            } else {
                cleanNumber
            }
            "+212$numberWithoutZero"
        }
    }
}
