plugins {
    id("avito.android.library")
    id("avito.android.library.compose")
}

android {
    namespace = "se.scmv.morocco.designsystem"

    flavorDimensions += "environment"
    productFlavors {
        create("prod") {
            dimension = "environment"
        }
        create("pre") {
            dimension = "environment"
        }
    }
}

dependencies {
    implementation(project(":core:domain"))
    implementation(project(":core:common"))
    //clarity
    implementation(libs.microsoft.clarity)
}