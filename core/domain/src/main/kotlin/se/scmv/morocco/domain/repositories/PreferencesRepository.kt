package se.scmv.morocco.domain.repositories

/**
 * Repository interface for managing app preferences.
 * This should replace the use of SharedPreferences in the app.
 */
interface PreferencesRepository {
    fun saveString(key: String, value: String)
    fun getString(key: String): String?
    fun getString(key: String, defaultValue: String): String
    fun saveBoolean(key: String, value: Boolean)
    fun getBoolean(key: String): Boolean
    fun getBoolean(key: String, defaultValue: Boolean): Boolean
    fun saveInt(key: String, value: Int)
    fun getInt(key: String): Int
    fun getInt(key: String, defaultValue: Int): Int
    fun saveLong(key: String, value: Long)
    fun getLong(key: String): Long
    fun getLong(key: String, defaultValue: Long): Long
}