package se.scmv.morocco.adstickybanner.presentation

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.flow.distinctUntilChanged
import kotlinx.coroutines.flow.map
import kotlinx.coroutines.launch
import se.scmv.morocco.adstickybanner.domain.AdManager
import se.scmv.morocco.adstickybanner.models.NotifAd
import se.scmv.morocco.adstickybanner.network.LeadRequest
import se.scmv.morocco.data.repository.utils.Constants
import se.scmv.morocco.domain.models.orion.OrionBaseComponentValue
import se.scmv.morocco.domain.models.orion.OrionMultipleSelectSmartDropDownValue
import se.scmv.morocco.domain.models.orion.OrionSingleSelectCategoryDropdownValue
import se.scmv.morocco.orion.presentation.OrionFiltersSharedValuesManager
import javax.inject.Inject

@HiltViewModel
class AdViewModel @Inject constructor(
    private val adManager: AdManager,
    private val sharedValuesManager: OrionFiltersSharedValuesManager,
) : ViewModel() {

    // Expose state flows from AdManager
    val imageSlideFlow: StateFlow<String> = adManager.imageSlideFlow
    val notifAdFlow: StateFlow<NotifAd?> = adManager.notifAdFlow
    val isImageSlideActive: StateFlow<Boolean> = adManager.isImageSlideActive
    val isNotificationAdActive: StateFlow<Boolean> = adManager.isNotificationAdActive

    init {
        // Initialize the ad manager when ViewModel is created
        adManager.initialize()
        viewModelScope.launch {
            sharedValuesManager.filterValueSharedFlow
                .map { filterData ->
                    FilterState(
                        selectedCategory = filterData.dynamicFilters.extractCategoryFilter() ?: "0",
                        selectedCities = filterData.dynamicFilters.extractCityFilter()
                    )
                }
                .distinctUntilChanged()
                .collectLatest { filterState ->
                    updateCategory(
                        categoryId = filterState.selectedCategory,
                        cities = filterState.selectedCities
                    )
                }
        }
    }

    override fun onCleared() {
        super.onCleared()
        // Cleanup when ViewModel is destroyed
        adManager.cleanup()
    }

    fun hideNotificationAd() {
        viewModelScope.launch {
            adManager.hideNotificationAd()
        }
    }

    fun recordImpression() {
        viewModelScope.launch {
            adManager.recordImpression()
        }
    }

    fun recordClick(openWebViewActivity: (String) -> Unit) {
        viewModelScope.launch {
            adManager.recordClick(openWebViewActivity)
        }
    }

    fun recordCreativeImpression() {
        viewModelScope.launch {
            adManager.recordCreativeImpression()
        }
    }

    fun recordCreativeClick() {
        viewModelScope.launch {
            adManager.recordCreativeClick()
        }
    }

    // User interactions
    fun onNotificationAdCtaClick() {
        viewModelScope.launch {
            adManager.onNotificationAdCtaClick()
        }
    }

    fun onStickyBannerClick() {
        viewModelScope.launch {
            adManager.onStickyBannerClick()
        }
    }

    fun storeLeads(leadRequest: LeadRequest) {
        viewModelScope.launch {
            adManager.storeLeads(leadRequest)
        }
    }

    // State management
    fun updateCategory(categoryId: String, cities: List<Int?>?) {
        viewModelScope.launch {
            adManager.updateCategory(categoryId, cities)
        }
    }

    fun refreshAdStates() {
        viewModelScope.launch {
            adManager.refreshAdStates()
        }
    }
}

private fun List<OrionBaseComponentValue>.extractCategoryFilter(): String? {
    return filterIsInstance<OrionSingleSelectCategoryDropdownValue>()
        .find { it.id == Constants.KEY_CATEGORY }
        ?.category?.id
}

private fun List<OrionBaseComponentValue>.extractCityFilter(): List<Int> {
    return filterIsInstance<OrionMultipleSelectSmartDropDownValue>()
        .find { it.id == Constants.KEY_CITY }
        ?.parents
        ?.mapNotNull { it.id.toIntOrNull() }
        ?: emptyList()
}

data class FilterState(
    val selectedCategory: String = "all",
    val selectedCities: List<Int> = emptyList()
)
