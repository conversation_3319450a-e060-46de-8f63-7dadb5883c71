# Add project specific ProGuard rules here.
# By default, the flags in this file are appended to flags specified
# in /Users/<USER>/Documents/Dev center/adt-bundle-mac-x86_64-20131030/sdk/tools/proguard/proguard-android.txt
# You can edit the include path and order by changing the proguardFiles
# directive in build.gradle.kts.
#
# For more details, see
#   http://developer.android.com/guide/developing/tools/proguard.html

# Add any project specific keep options here:

# If your project uses WebView with JS, uncomment the following
# and specify the fully qualified class name to the JavaScript interface
# class:
#-keepclassmembers class fqcn.of.javascript.interface.for.webview {
#   public *;
#}
-keepattributes Signature
-dontwarn com.amazon.device.messaging.**
-dontwarn bo.app.**
-keep class bo.app.** { *; }
-keep class com.braze.** { *; }
-dontwarn com.google.android.gms.**
-keep class com.google.android.gms { *; }
-keep class com.google.android.gms.location.** { *; }
-keep class com.google.android.gms.internal.** { *; }
-keep class bo.app.** { *; }
-keep class com.appboy.** { *; }
-dontnote android.net.http.*
-dontnote org.apache.commons.codec.**
-dontnote org.apache.http.**
-dontwarn com.firebase.ui.auth.data.remote.**
-keep class androidx.v4.app.** { *; }
-keep interface androidx.v4.app.** { *; }
-dontwarn com.google.protobuf.java_com_google_android_gmscore_sdk_target_granule__proguard_group_gtm_N1281923064GeneratedExtensionRegistryLite$Loader

# This is generated automatically by the Android Gradle plugin.
-dontwarn com.gemalto.jp2.JP2Decoder
# Keep native methods
-keepclassmembers class * {
    native <methods>;
}
# Keep Gson classes
-keepattributes Signature
-keepattributes *Annotation*

# Keep DTOs used by Gson
-keep class se.scmv.morocco.data.database.daos.** { *; }

# Prevent Gson from stripping out any model
-keep class com.google.gson.** { *; }

# Keep any generic type info (needed for Map<String, T>)
-keepclassmembers class * {
    @com.google.gson.annotations.SerializedName <fields>;
}

#------------------------------------CORE------------------------------------------

-dontwarn okio.**
-dontwarn com.squareup.okhttp.**
-dontwarn javax.annotation.**
-dontwarn org.xmlpull.v1.**
-dontwarn com.esafirm.imagepicker.**
-dontwarn okhttp3.internal.platform.**
-dontwarn retrofit2.Platform$Java8
#ATInternet config
-keep class com.atinternet.*** { *; }

#greenRobot EventBus
-keepattributes *Annotation*
-keepclassmembers class ** {
 @org.greenrobot.eventbus.Subscribe <methods>;
}
-keep enum org.greenrobot.eventbus.ThreadMode { *; }

# Only required if you use AsyncExecutor
-keepclassmembers class * extends org.greenrobot.eventbus.util.ThrowableFailureEvent {
 <init>(java.lang.Throwable);
}

#Apteligent
-dontwarn com.crittercism.**
-keep public class com.crittercism.**
-keepclassmembers public class com.crittercism.**{
 *;
}
-keepattributes SourceFile, LineNumberTable
#keep custom exceptions (opt)
-keep public class * extends java.lang.Exception

-dontwarn org.apache.commons.**
-keep class org.apache.http.** { *; }
-dontwarn org.apache.http.**



#tayara json parsing not working so added following.
-dontskipnonpubliclibraryclassmembers

-keepattributes *Annotation*,EnclosingMethod

-keepnames class org.codehaus.jackson.** { *; }

-dontwarn javax.xml.**
-dontwarn javax.xml.stream.events.**


#GLide
-dontwarn com.bumptech.glide.load.resource.bitmap.VideoDecoder
-keep public class * implements com.bumptech.glide.module.GlideModule
-keep public class * extends com.bumptech.glide.module.AppGlideModule
-keep public enum com.bumptech.glide.load.ImageHeaderParser$** {
  **[] $VALUES;
  public *;
}
-keep class com.google.gson.reflect.TypeToken
-keep class * extends com.google.gson.reflect.TypeToken
-keep public class * implements java.lang.reflect.Type
-keep class sun.misc.** { *; }
-keep class com.google.** { *; }
#Orion
-keep public interface ma.avito.orion.ui.** { *;}
-keep class ma.avito.orion.ui.** { *;}
#XML
-keep class org.xmlpull.v1.** { *;}
-dontwarn org.xmlpull.v1.**

# Retrofit 2.X
## https://square.github.io/retrofit/ ##
-dontwarn retrofit2.**
-keep class retrofit2.** { *; }
-keepattributes Signature
-keepattributes Exceptions
-keepclasseswithmembers class * {
    @retrofit2.http.* <methods>;
}
# Platform calls Class.forName on types which do not exist on Android to determine platform.
-dontnote retrofit2.Platform
# Platform used when running on RoboVM on iOS. Will not be used at runtime.
-dontnote retrofit2.Platform$IOS$MainThreadExecutor
# Platform used when running on Java 8 VMs. Will not be used at runtime.
-dontwarn retrofit2.Platform$Java8
-dontwarn com.google.auto.**
-dontwarn autovalue.shaded.com.**
-dontwarn javax.validation.constraints.Pattern
# Keep generic signature of Call, Response (R8 full mode strips signatures from non-kept items).
 -keep,allowobfuscation,allowshrinking interface retrofit2.Call
 -keep,allowobfuscation,allowshrinking class retrofit2.Response
 -keep,allowobfuscation,allowshrinking class kotlin.coroutines.Continuation

 # R8 full mode strips generic signatures from return types if not kept.
 -if interface * { @retrofit2.http.* public *** *(...); }
 -keep,allowoptimization,allowshrinking,allowobfuscation class <3>

-keepnames class kotlinx.coroutines.internal.MainDispatcherFactory {}
-keepnames class kotlinx.coroutines.CoroutineExceptionHandler {}
-keepnames class kotlinx.coroutines.android.AndroidExceptionPreHandler {}
-keepnames class kotlinx.coroutines.android.AndroidDispatcherFactory {}

#------------------------------------FEATURES------------------------------------------

#---Common----
-keep public class se.scmv.morocco.models.**
-keep public class se.scmv.morocco.models.** {
  *;
}

#---Similar Ads ----
-keep class se.scmv.morocco.similarads.**

#---Sign In ----
-keep  class se.scmv.morocco.login.signin.model.** {
public protected private *;
}

#---CONFIG Loader----
-keep class se.scmv.morocco.configloader.** { *; }

#---Ad Insertion----
-keep class se.scmv.morocco.adinsert.** { *; }
#---COGNITION----
-keep public class se.scmv.morocco.cognition.** { *; }
-keep public class se.scmv.morocco.cognition.analytics.** { *; }

#---VAS Gallery ----
-keep  class se.scmv.morocco.vas.vasgallery.models.** {
public protected private *;
}
#---Media----
-keep  class se.scmv.morocco.media.config.** {
public protected private *;
}
#---Messaging----
-keep class se.scmv.morocco.messagingui.providers.** { *; }
#----New Construction
-keep class se.scmv.morocco.immoneuf.model.** { public protected private *; }
-keep public class com.android.installreferrer.** { *; }
-keep public class com.google.firebase.messaging.FirebaseMessagingService {
  public *;
}
#----ChatKit
-keep class * extends com.stfalcon.chatkit.messages.MessageHolders$OutcomingTextMessageViewHolder {
     public <init>(android.view.View, java.lang.Object);
     public <init>(android.view.View);
 }
-keep class * extends com.stfalcon.chatkit.messages.MessageHolders$IncomingTextMessageViewHolder {
     public <init>(android.view.View, java.lang.Object);
     public <init>(android.view.View);
 }
-keep class * extends com.stfalcon.chatkit.messages.MessageHolders$IncomingImageMessageViewHolder {
     public <init>(android.view.View, java.lang.Object);
     public <init>(android.view.View);
 }
-keep class * extends com.stfalcon.chatkit.messages.MessageHolders$OutcomingImageMessageViewHolder {
     public <init>(android.view.View, java.lang.Object);
     public <init>(android.view.View);
 }

 -if class androidx.credentials.CredentialManager
 -keep class androidx.credentials.playservices.** {
   *;
 }

 -dontwarn com.gemalto.**

 -dontwarn com.here.android.mpa.common.GeoCoordinate
 -dontwarn com.here.android.mpa.common.Image
 -dontwarn com.here.android.mpa.common.OnEngineInitListener$Error
 -dontwarn com.here.android.mpa.common.OnEngineInitListener
 -dontwarn com.here.android.mpa.mapping.AndroidXMapFragment
 -dontwarn com.here.android.mpa.mapping.Map$Animation
 -dontwarn com.here.android.mpa.mapping.Map
 -dontwarn com.here.android.mpa.mapping.MapMarker
 -dontwarn com.here.android.mpa.mapping.MapObject