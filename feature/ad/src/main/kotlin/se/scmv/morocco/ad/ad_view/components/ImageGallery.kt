package se.scmv.morocco.ad.ad_view.components

import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.WindowInsets
import androidx.compose.foundation.layout.WindowInsetsSides
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.navigationBars
import androidx.compose.foundation.layout.navigationBarsPadding
import androidx.compose.foundation.layout.only
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Icon
import androidx.compose.material3.IconButton
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.ModalBottomSheet
import androidx.compose.material3.SheetValue
import androidx.compose.material3.Text
import androidx.compose.material3.rememberModalBottomSheetState
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import coil.compose.AsyncImage
import coil.compose.rememberImagePainter
import com.google.android.exoplayer2.ExoPlayer
import com.google.android.exoplayer2.MediaItem
import com.google.android.exoplayer2.Player
import com.google.android.exoplayer2.ui.StyledPlayerView
import kotlinx.coroutines.launch
import net.engawapg.lib.zoomable.rememberZoomState
import net.engawapg.lib.zoomable.zoomable
import se.scmv.morocco.ad.R
import se.scmv.morocco.designsystem.theme.dimens
import se.scmv.morocco.domain.models.AdDetails

/** Displays a horizontally scrolling pager for ad images. */
@Composable
fun AdImagesSlider(
    images: List<AdDetails.Details.Image?>,
    videoUrl: String?,
    isBoutique: Boolean,
    defaultImageCount: Int? = null,
    onImageClick: (Int, List<AdDetails.Details.Image?>, String?) -> Unit,
    index: Int,
    exoPlayer: ExoPlayer,
    seller: AdDetails.Seller? = null,
    onWhatsAppClick: (() -> Unit)? = null,
    onMessageClick: (() -> Unit)? = null,
    onCallClick: (() -> Unit)? = null,
    carCheckConfig: se.scmv.morocco.domain.models.CarCheckConfig? = null,
    onCarCheckClicked: (() -> Unit)? = null
) {

    val pageCount = when {
        videoUrl.isNullOrEmpty() -> images.size + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
        else -> images.size + 1 + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
    }
    val pagerState = rememberPagerState(initialPage = index, pageCount = { pageCount })

    LaunchedEffect(index) {
        if (pagerState.currentPage != index) {
            pagerState.scrollToPage(index)
        }
    }
    Box {
        if (images.isEmpty()) {
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(400.dp)
            ) {

                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(MaterialTheme.colorScheme.onSurface.copy(alpha = 0.03f))
                        .clickable {

                        },
                    contentAlignment = Alignment.Center
                ) {
                    Icon(
                        painter = painterResource(R.drawable.ic_no_image),
                        contentDescription = "No Image",
                        modifier = Modifier.size(48.dp),
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                }
            }

        } else {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(400.dp)
            ) { page ->
                val isCarCheckPage = when {
                    videoUrl.isNullOrEmpty() -> page == images.size && carCheckConfig != null
                    else -> page == images.size + 1 && carCheckConfig != null
                }

                val isContactSellerPage = when {
                    videoUrl.isNullOrEmpty() -> page == images.size + (if (carCheckConfig != null) 1 else 0) && seller != null
                    else -> page == images.size + 1 + (if (carCheckConfig != null) 1 else 0) && seller != null
                }

                if (isCarCheckPage) {
                    CarCheckSlide(
                        carCheckConfig = carCheckConfig!!,
                        onInspectClicked = { onCarCheckClicked?.invoke() },
                        modifier = Modifier.fillMaxSize()
                    )
                } else if (isContactSellerPage) {
                    ContactSellerSlide(
                        seller = seller,
                        onWhatsAppClick = { onWhatsAppClick?.invoke() },
                        onMessageClick = { onMessageClick?.invoke() },
                        onCallClick = { onCallClick?.invoke() },
                        modifier = Modifier.fillMaxSize()
                    )
                } else if (videoUrl != null && page == 0) {
                    VideoPlayer(
                        exoPlayer = exoPlayer, videoUrl = videoUrl,
                        imageUrl = images?.firstOrNull()?.paths?.standard.orEmpty(),
                        fullScreen = false,
                        onFullscreenClick = {
                            onImageClick(page, images, videoUrl)
                        }
                    )
                } else {
                    if (exoPlayer.isPlaying)
                        exoPlayer.pause()
                    AsyncImage(
                        model = if (videoUrl.isNullOrEmpty()) images?.get(page)?.paths?.standard.orEmpty() else images?.get(
                            page - 1
                        )?.paths?.standard.orEmpty(),
                        contentDescription = "Ad Image",
                        modifier = Modifier
                            .fillMaxSize()
                            .clickable {
                                onImageClick(page, images, videoUrl)
                            },
                        contentScale = ContentScale.Crop
                    )
                }

            }
        }


        val totalSlides = when {
            videoUrl.isNullOrEmpty() -> images.size + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
            else -> images.size + 1 + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
        }

        val imageCount = when {
            // Car check page
            (videoUrl.isNullOrEmpty() && pagerState.currentPage == images.size && carCheckConfig != null) ||
                    (videoUrl != null && pagerState.currentPage == images.size + 1 && carCheckConfig != null) -> ""
            // Contact seller page
            (videoUrl.isNullOrEmpty() && pagerState.currentPage == images.size + (if (carCheckConfig != null) 1 else 0) && seller != null) ||
                    (videoUrl != null && pagerState.currentPage == images.size + 1 + (if (carCheckConfig != null) 1 else 0) && seller != null) -> ""
            // Video page
            videoUrl != null && pagerState.currentPage == 0 -> totalSlides
            defaultImageCount != null && defaultImageCount > 0 && pagerState.currentPage == 0 -> "1/$totalSlides"
            defaultImageCount == 0 -> "0"
            videoUrl != null && pagerState.currentPage > 0 -> "${pagerState.currentPage + 1}/$totalSlides"
            else -> "${pagerState.currentPage + 1}/$totalSlides"
        }



        if (imageCount != "") {
            MediaInfoBar(
                hasVideo = !videoUrl.isNullOrEmpty(),
                imageCount = imageCount.toString(),
                isBoutique = isBoutique,
                modifier = Modifier
                    .align(Alignment.BottomStart)
                    .padding(MaterialTheme.dimens.medium)
            )
        }
    }
}


@Composable
fun VideoPlayer(
    exoPlayer: ExoPlayer,
    videoUrl: String,
    imageUrl: String,
    fullScreen: Boolean = false,
    onFullscreenClick: () -> Unit

) {
    var isPlayerInitialized by remember {
        mutableStateOf(exoPlayer.playbackState != Player.STATE_IDLE)
    }

    Box(modifier = Modifier.fillMaxSize()) {
        // ExoPlayer view
        AndroidView(
            factory = { context ->
                StyledPlayerView(context).apply {
                    player = exoPlayer
                    layoutParams = FrameLayout.LayoutParams(
                        ViewGroup.LayoutParams.MATCH_PARENT,
                        ViewGroup.LayoutParams.MATCH_PARENT
                    )
                    useController = true
                    setShowNextButton(false)
                    setShowPreviousButton(false)
                }
            },
            modifier = Modifier.fillMaxSize()
        )

        // Thumbnail overlay before video starts
        if (!isPlayerInitialized) {
            Image(
                painter = rememberImagePainter(imageUrl),
                contentDescription = null,
                modifier = Modifier
                    .fillMaxSize()
                    .align(Alignment.Center)
                    .clickable {
                        onFullscreenClick()
                    },
                contentScale = ContentScale.Crop
            )
        }

        // Play button overlay
        if (!isPlayerInitialized) {
            IconButton(
                onClick = {
                    val mediaItem = MediaItem.fromUri(videoUrl)
                    exoPlayer.setMediaItem(mediaItem)
                    exoPlayer.prepare()
                    isPlayerInitialized = true
                    exoPlayer.playWhenReady = true
                    isPlayerInitialized = true
                },
                modifier = Modifier
                    .size(60.dp)
                    .align(Alignment.Center)
                    .background(Color.Black.copy(alpha = 0.5f), shape = CircleShape)
            ) {
                Icon(
                    imageVector = Icons.Default.PlayArrow,
                    contentDescription = "Play",
                    tint = Color.White,
                    modifier = Modifier.size(36.dp)
                )
            }
        } else if (!fullScreen) {
            IconButton(
                onClick = {
                    exoPlayer.stop()
                    onFullscreenClick()
                },
                modifier = Modifier
                    .size(60.dp)
                    .align(Alignment.BottomEnd)
                    .padding(end = 36.dp)
                    .background(Color.Black.copy(alpha = 0.5f), shape = CircleShape)
            ) {
                Icon(
                    painter = painterResource(R.drawable.ic_fullscreen),
                    contentDescription = "Play",
                    tint = Color.White,
                    modifier = Modifier.size(MaterialTheme.dimens.bigger)
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FullScreenImageSlider(
    images: List<AdDetails.Details.Image?>,
    initialPageIndex: Int,
    videoUrl: String?,
    onDismiss: (index: Int) -> Unit,
    exoPlayer: ExoPlayer,
    seller: AdDetails.Seller? = null,
    onWhatsAppClick: (() -> Unit)? = null,
    onMessageClick: (() -> Unit)? = null,
    onCallClick: (() -> Unit)? = null,
    carCheckConfig: se.scmv.morocco.domain.models.CarCheckConfig? = null,
    onCarCheckClicked: (() -> Unit)? = null
) {
    val pageCount = when {
        videoUrl.isNullOrEmpty() -> images.size + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
        else -> images.size + 1 + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
    }
    val pagerState =
        rememberPagerState(initialPage = initialPageIndex, pageCount = { pageCount })
    val scope = rememberCoroutineScope()
    val sheetState = rememberModalBottomSheetState(
        skipPartiallyExpanded = true,
        confirmValueChange = { newValue ->
            // Prevent dismiss no matter what
            newValue != SheetValue.Hidden
        }
    )

    val zoomState = rememberZoomState(
        maxScale = 5f
    )

// Reset Zoom when we change page
    LaunchedEffect(pagerState.currentPage) {
        zoomState.reset()
    }

    // Reset Zoom when we change page
    LaunchedEffect(pagerState.currentPage) {
        zoomState.reset()
    }



    ModalBottomSheet(
        onDismissRequest = {
            scope.launch { sheetState.hide() }
                .invokeOnCompletion { onDismiss(pagerState.currentPage) }
        },
        sheetState = sheetState,
        modifier = Modifier.fillMaxSize(), // Fill the entire screen
        containerColor = Color.Black,
        contentColor = Color.White,
        shape = RoundedCornerShape(MaterialTheme.dimens.none),
        contentWindowInsets = { WindowInsets.navigationBars.only(WindowInsetsSides.Horizontal) }

    ) {
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            HorizontalPager(
                state = pagerState,
                modifier = Modifier.fillMaxSize()
            ) { page ->
                val isCarCheckPage = when {
                    videoUrl.isNullOrEmpty() -> page == images.size && carCheckConfig != null
                    else -> page == images.size + 1 && carCheckConfig != null
                }

                val isContactSellerPage = when {
                    videoUrl.isNullOrEmpty() -> page == images.size + (if (carCheckConfig != null) 1 else 0) && seller != null
                    else -> page == images.size + 1 + (if (carCheckConfig != null) 1 else 0) && seller != null
                }

                if (isCarCheckPage) {
                    CarCheckSlide(
                        carCheckConfig = carCheckConfig!!,
                        onInspectClicked = { onCarCheckClicked?.invoke() },
                        modifier = Modifier.fillMaxSize()
                    )
                } else if (isContactSellerPage) {
                    // Contact Seller Slide
                    ContactSellerSlide(
                        seller = seller,
                        onWhatsAppClick = { onWhatsAppClick?.invoke() },
                        onMessageClick = { onMessageClick?.invoke() },
                        onCallClick = { onCallClick?.invoke() },
                        modifier = Modifier.fillMaxSize()
                    )
                } else if (videoUrl != null && page == 0) {
                    VideoPlayer(
                        exoPlayer = exoPlayer, videoUrl = videoUrl,
                        imageUrl = images[0]?.paths?.standard.orEmpty(),
                        fullScreen = true,
                        onFullscreenClick = {

                        }
                    )
                } else {
                    if (exoPlayer.isPlaying)
                        exoPlayer.pause()
                    AsyncImage(
                        model = if (videoUrl.isNullOrEmpty()) images[page]?.paths?.standard.orEmpty() else images[page - 1]?.paths?.standard.orEmpty(),
                        contentDescription = "Ad Image",
                        modifier = Modifier
                            .fillMaxSize()
                            .zoomable(zoomState),
                        contentScale = ContentScale.Fit
                    )
                }
            }
            IconButton(
                onClick = {
                    scope.launch {
                        sheetState.hide()
                    }.invokeOnCompletion {
                        onDismiss(pagerState.currentPage)
                    }
                },
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(MaterialTheme.dimens.default)
            ) {
                Icon(
                    Icons.Filled.Close, 
                    contentDescription = "Close", 
                    tint = Color.White,
                    modifier = Modifier
                        .background(
                            Color.Black.copy(alpha = 0.6f),
                            CircleShape
                        )
                        .padding(4.dp)
                )
            }

            val totalSlides = when {
                videoUrl.isNullOrEmpty() -> images.size + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
                else -> images.size + 1 + (if (carCheckConfig != null) 1 else 0) + (if (seller != null) 1 else 0)
            }

            val imageCount = when {
                // Car check page
                (videoUrl.isNullOrEmpty() && pagerState.currentPage == images.size && carCheckConfig != null) ||
                        (videoUrl != null && pagerState.currentPage == images.size + 1 && carCheckConfig != null) -> ""
                // Contact seller page
                (videoUrl.isNullOrEmpty() && pagerState.currentPage == images.size + (if (carCheckConfig != null) 1 else 0) && seller != null) ||
                        (videoUrl != null && pagerState.currentPage == images.size + 1 + (if (carCheckConfig != null) 1 else 0) && seller != null) -> ""

                else -> "${pagerState.currentPage + 1} / $totalSlides"
            }
            Text(
                text = imageCount,
                color = Color.White,
                fontSize = 16.sp,
                modifier = Modifier
                    .align(Alignment.BottomCenter)
                    .padding(bottom = 28.dp)
                    .navigationBarsPadding()
                    .fillMaxWidth(),
                textAlign = TextAlign.Center
            )

        }
    }
}
